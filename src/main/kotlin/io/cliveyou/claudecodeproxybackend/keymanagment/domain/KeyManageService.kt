package io.cliveyou.claudecodeproxybackend.keymanagment.domain

import io.cliveyou.claudecodeproxybackend.KeyScheduler
import io.cliveyou.claudecodeproxybackend.SchedulerConfig
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import kotlin.time.Duration

/**
 * 包含调度器配置的密钥数据类
 */
data class KeyWithSchedulerConfig(
    val commonTokenKey: CommonTokenKey,
    val weight: Double,
    val windowSize: Int,
    val epsilon: Double,
    val errThreshold: Double,
    val minSampleSize: Int
)

@Service
class KeyManageService(
    private val tokenManagementService: TokenManagementService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository
) {

    companion object {
        private val log = KotlinLogging.logger {}
    }

    // 为每个模型维护独立的读写锁，减少锁竞争
    private val modelLocks = ConcurrentHashMap<String, ReentrantReadWriteLock>()

    // 主存储：按模型名称映射到一组可用的CommonTokenKey
    private val modelKeysMap = ConcurrentHashMap<String, MutableList<CommonTokenKey>>()

    // 密钥到模型的反向索引
    private val keyToModelsIndex = ConcurrentHashMap<Long, MutableSet<String>>()
    
    // 每个模型的密钥调度器，用于智能选择密钥
    private val modelSchedulers = ConcurrentHashMap<String, KeyScheduler>()

    private val scheduler: ScheduledExecutorService = Executors.newScheduledThreadPool(2)

    /**
     * 获取指定模型的读写锁，如果不存在则创建
     */
    private fun getModelLock(modelName: String): ReentrantReadWriteLock {
        return modelLocks.computeIfAbsent(modelName) { ReentrantReadWriteLock() }
    }

    /**
     * 加载模型的密钥映射
     * @param modelName 模型名称
     * @param keysWithConfig 该模型可用的密钥列表，包含调度器配置
     */
    fun loadModelKeys(modelName: String, keysWithConfig: List<KeyWithSchedulerConfig>) {
        val startTime = System.currentTimeMillis()
        log.info { "Load Key | Loading ${keysWithConfig.size} keys for model $modelName" }

        getModelLock(modelName).write {
            // 清理旧数据
            modelKeysMap[modelName]?.clear()

            // 计算模型级别的调度器配置（取平均值或使用第一个密钥的配置）
            val firstKeyConfig = keysWithConfig.firstOrNull()
            val schedulerConfig = if (firstKeyConfig != null) {
                SchedulerConfig(
                    windowSize = firstKeyConfig.windowSize,
                    epsilon = firstKeyConfig.epsilon,
                    errThreshold = firstKeyConfig.errThreshold
                )
            } else {
                SchedulerConfig()
            }

            // 初始化存储
            val modelKeys = modelKeysMap.getOrPut(modelName) { mutableListOf() }
            // 重新创建调度器以清理旧的统计数据
            val scheduler = KeyScheduler(schedulerConfig)
            modelSchedulers[modelName] = scheduler

            // 加载新数据并建立双向索引
            keysWithConfig.forEach { keyConfig ->
                // 主存储
                modelKeys.add(keyConfig.commonTokenKey)

                // 反向索引
                keyToModelsIndex.getOrPut(keyConfig.commonTokenKey.id!!) { ConcurrentHashMap.newKeySet() }.add(modelName)

                // 初始化调度器中的密钥，使用数据库中的权重
                scheduler.upsertKey(keyConfig.commonTokenKey.id.toString(), keyConfig.weight)
            }

            val duration = System.currentTimeMillis() - startTime
            log.info { "Load Key | Successfully loaded ${keysWithConfig.size} keys for model $modelName | Duration: ${duration}ms" }
        }
    }

    /**
     * 批量加载所有模型的密钥映射
     * @param modelKeysMapping 模型名称到密钥配置列表的映射
     */
    fun loadAllModelKeys(modelKeysMapping: Map<String, List<KeyWithSchedulerConfig>>) {
        val startTime = System.currentTimeMillis()
        log.info { "Load Key | Loading keys for ${modelKeysMapping.size} models" }

        modelKeysMapping.forEach { (modelName, keysWithConfig) ->
            loadModelKeys(modelName, keysWithConfig)
        }

        val totalKeys = modelKeysMapping.values.sumOf { it.size }
        val duration = System.currentTimeMillis() - startTime
        log.info { "Load Key | Successfully loaded $totalKeys total keys across ${modelKeysMapping.size} models | Duration: ${duration}ms" }
    }

    /**
     * 根据模型名称获取最优密钥
     * @param modelName 模型名称
     * @param channels 可选的渠道列表，如果指定则只从这些渠道的密钥中选择
     * @return 选中的密钥
     */
    fun findBestAvailableKey(modelName: String, channels: List<KeyChannel>? = null): CommonTokenKey {
        val startTime = System.currentTimeMillis()
        return getModelLock(modelName).read {
            val allModelKeys = modelKeysMap[modelName]
            val scheduler = modelSchedulers[modelName]

            if (allModelKeys.isNullOrEmpty()) {
                val duration = System.currentTimeMillis() - startTime
                log.error { "Fetch Key | Request for model: $modelName | Failed: Empty key list | Duration: ${duration}ms | Suggestion: Add keys for this model" }
                throw IllegalStateException("No available keys found for model $modelName")
            }

            // 根据渠道过滤密钥
            val modelKeys = if (channels.isNullOrEmpty()) {
                allModelKeys
            } else {
                val filteredKeys = allModelKeys.filter { key -> key.type in channels }
                if (filteredKeys.isEmpty()) {
                    val duration = System.currentTimeMillis() - startTime
                    log.error { "Fetch Key | Request for model: $modelName, channels: $channels | Failed: No keys found for specified channels | Duration: ${duration}ms | Suggestion: Add keys for these channels" }
                    throw IllegalStateException("No available keys found for model $modelName with channels $channels")
                }
                filteredKeys
            }

            if (scheduler == null) {
                val duration = System.currentTimeMillis() - startTime
                log.error { "Fetch Key | Request for model: $modelName | Failed: No scheduler found | Duration: ${duration}ms | Suggestion: Initialize scheduler for this model" }
                throw IllegalStateException("No scheduler found for model $modelName")
            }

            // 如果指定了渠道过滤，需要从过滤后的密钥中选择
            val availableKeyIds = modelKeys.map { it.id!!.toString() }.toSet()
            val selectedKeyId = if (channels.isNullOrEmpty()) {
                // 没有渠道过滤，使用原有逻辑
                scheduler.chooseKey()
            } else {
                // 有渠道过滤，需要从过滤后的密钥中选择
                scheduler.chooseKeyFromCandidates(availableKeyIds)
            }

            if (selectedKeyId == null) {
                val duration = System.currentTimeMillis() - startTime
                val channelInfo = if (channels.isNullOrEmpty()) "" else ", channels: $channels"
                log.error { "Fetch Key | Request for model: $modelName$channelInfo | Failed: No available key from scheduler | Duration: ${duration}ms | Suggestion: Check key error rates" }
                throw IllegalStateException("No available key found by scheduler for model $modelName${if (channels.isNullOrEmpty()) "" else " with channels $channels"}")
            }

            // 根据选中的密钥ID找到对应的CommonTokenKey
            val selectedKey = modelKeys.find { it.id!!.toString() == selectedKeyId }
            if (selectedKey == null) {
                val duration = System.currentTimeMillis() - startTime
                val channelInfo = if (channels.isNullOrEmpty()) "" else ", channels: $channels"
                log.error { "Fetch Key | Request for model: $modelName$channelInfo | Failed: Selected key not found in model keys | Duration: ${duration}ms | Suggestion: Check key consistency" }
                throw IllegalStateException("Selected key $selectedKeyId not found in model $modelName keys")
            }

            val duration = System.currentTimeMillis() - startTime
            val channelInfo = if (channels.isNullOrEmpty()) "" else ", channels: $channels"
            log.info { "Fetch Key | Request for model: $modelName$channelInfo | Success: Found key ${selectedKey.id} (channel: ${selectedKey.type}) | Duration: ${duration}ms" }
            selectedKey
        }
    }

    /**
     * 记录密钥使用结果，用于调度器学习
     * @param modelName 模型名称
     * @param keyId 密钥ID
     * @param success 是否成功
     * @param needDeleted 是否需要删除密钥（密钥失效时为true）
     */
    fun recordKeyUsageResult(modelName: String, keyId: Long, success: Boolean, needDeleted: Boolean = false) {
        val startTime = System.currentTimeMillis()
        getModelLock(modelName).read {
            val scheduler = modelSchedulers[modelName]
            if (scheduler != null) {
                scheduler.recordResult(keyId.toString(), success)
                val duration = System.currentTimeMillis() - startTime
                log.debug { "Record Result | Model: $modelName, Key: $keyId, Success: $success, NeedDeleted: $needDeleted | Duration: ${duration}ms" }
            } else {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Record Result | Model: $modelName, Key: $keyId | Failed: No scheduler found | Duration: ${duration}ms" }
            }
        }

        // 如果需要删除密钥，执行禁用逻辑
        if (needDeleted) {
            disableKey(keyId)
        }
    }

    /**
     * 更新密钥权重
     * @param keyId 密钥ID
     * @param weight 新权重
     */
    fun updateKeyWeight(keyId: Long, weight: Double) {
        val startTime = System.currentTimeMillis()
        val models = keyToModelsIndex[keyId]
        if (models.isNullOrEmpty()) {
            val duration = System.currentTimeMillis() - startTime
            log.warn { "Update Weight | Key $keyId not found in any model | Duration: ${duration}ms" }
            return
        }

        models.forEach { modelName ->
            getModelLock(modelName).read {
                val scheduler = modelSchedulers[modelName]
                scheduler?.upsertKey(keyId.toString(), weight)
            }
        }
        
        val duration = System.currentTimeMillis() - startTime
        log.info { "Update Weight | Key $keyId weight updated to $weight in ${models.size} models | Duration: ${duration}ms" }
    }

    /**
     * 添加密钥到指定模型
     * @param modelName 模型名称
     * @param key 要添加的密钥
     * @param weight 密钥权重，默认为1.0
     */
    fun addKeyToModel(modelName: String, key: CommonTokenKey, weight: Double = 1.0) {
        val startTime = System.currentTimeMillis()
        getModelLock(modelName).write {
            val modelKeys = modelKeysMap.getOrPut(modelName) { mutableListOf() }
            val scheduler = modelSchedulers.getOrPut(modelName) { KeyScheduler(SchedulerConfig()) }

            // 检查密钥是否已存在
            if (modelKeys.any { it.id == key.id }) {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Add Key | Key ${key.id} already exists in model $modelName | Duration: ${duration}ms" }
                return@write
            }

            // 添加密钥
            modelKeys.add(key)
            keyToModelsIndex.getOrPut(key.id!!) { ConcurrentHashMap.newKeySet() }.add(modelName)
            scheduler.upsertKey(key.id.toString(), weight)

            val duration = System.currentTimeMillis() - startTime
            log.info { "Add Key | Key ${key.id} added to model $modelName with weight $weight | Duration: ${duration}ms" }
        }
    }

    /**
     * 从指定模型中移除密钥
     * @param modelName 模型名称
     * @param keyId 要移除的密钥ID
     */
    fun removeKeyFromModel(modelName: String, keyId: Long) {
        val startTime = System.currentTimeMillis()
        getModelLock(modelName).write {
            val modelKeys = modelKeysMap[modelName]
            val scheduler = modelSchedulers[modelName]

            if (modelKeys == null) {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Remove Key | Model $modelName not found | Duration: ${duration}ms" }
                return@write
            }

            // 移除密钥
            val removed = modelKeys.removeIf { it.id!! == keyId }
            if (removed) {
                // 从调度器中移除密钥
                scheduler?.removeKey(keyId.toString())

                // 更新反向索引
                keyToModelsIndex[keyId]?.remove(modelName)
                if (keyToModelsIndex[keyId]?.isEmpty() == true) {
                    keyToModelsIndex.remove(keyId)
                }

                val duration = System.currentTimeMillis() - startTime
                log.info { "Remove Key | Key $keyId removed from model $modelName (including scheduler) | Duration: ${duration}ms" }
            } else {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Remove Key | Key $keyId not found in model $modelName | Duration: ${duration}ms" }
            }
        }
    }

    /**
     * 移除密钥（从所有模型中移除）
     * @param key 要移除的密钥
     */
    fun removeKey(key: CommonTokenKey) {
        val startTime = System.currentTimeMillis()
        val models = keyToModelsIndex[key.id!!]?.toList() ?: emptyList()

        models.forEach { modelName ->
            removeKeyFromModel(modelName, key.id!!)
        }

        val duration = System.currentTimeMillis() - startTime
        log.info { "Remove Key | Key ${key.id} removed from ${models.size} models | Duration: ${duration}ms" }
    }

    /**
     * 更新密钥信息
     * @param updatedKey 更新后的密钥
     */
    fun updateKey(updatedKey: CommonTokenKey) {
        val startTime = System.currentTimeMillis()
        val models = keyToModelsIndex[updatedKey.id!!]

        if (models.isNullOrEmpty()) {
            // 如果密钥不在任何模型中，且密钥是活跃状态且有访问令牌，则根据supportModels添加到相应模型
            if (updatedKey.status == CommonKeyStatus.ACTIVE && !updatedKey.accessToken.isNullOrBlank()) {
                log.info { "Update Key | Key ${updatedKey.id} not found in any model, adding to supported models: ${updatedKey.supportModels}" }

                updatedKey.supportModels.forEach { modelName ->
                    addKeyToModel(modelName, updatedKey, updatedKey.weight.toDouble())
                }

                val duration = System.currentTimeMillis() - startTime
                log.info { "Update Key | Key ${updatedKey.id} added to ${updatedKey.supportModels.size} models | Duration: ${duration}ms" }
            } else {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Update Key | Key ${updatedKey.id} not found in any model and not active/valid | Status: ${updatedKey.status} | HasToken: ${!updatedKey.accessToken.isNullOrBlank()} | Duration: ${duration}ms" }
            }
            return
        }

        models.forEach { modelName ->
            getModelLock(modelName).write {
                val modelKeys = modelKeysMap[modelName]
                if (modelKeys != null) {
                    // 找到并更新密钥
                    val index = modelKeys.indexOfFirst { it.id!! == updatedKey.id }
                    if (index >= 0) {
                        modelKeys[index] = updatedKey
                    }
                }
            }
        }

        val duration = System.currentTimeMillis() - startTime
        log.info { "Update Key | Key ${updatedKey.id} updated in ${models.size} models | Duration: ${duration}ms" }
    }

    /**
     * 临时冻结密钥（设置高错误率）
     * @param key 要冻结的密钥
     * @param coldDownTime 冻结时间
     */
    fun freezeKey(key: CommonTokenKey, coldDownTime: Duration) {
        val startTime = System.currentTimeMillis()
        val models = keyToModelsIndex[key.id!!]?.toList() ?: emptyList()

        // 记录失败结果以提高错误率
        models.forEach { modelName ->
            val scheduler = modelSchedulers[modelName]
            // 连续记录多次失败以快速提高错误率
            repeat(10) {
                scheduler?.recordResult(key.id.toString(), false)
            }
        }

        // 安排解冻任务
        scheduler.schedule({
            val unfreezeStartTime = System.currentTimeMillis()
            try {
                // 重置密钥的统计信息（通过重新注册）
                models.forEach { modelName ->
                    val scheduler = modelSchedulers[modelName]
                    scheduler?.upsertKey(key.id.toString(), 1.0) // 重置为默认权重
                }
                val unfreezeDuration = System.currentTimeMillis() - unfreezeStartTime
                log.info { "Unfreeze Key | Cooldown period completed for ${key.id} | Success: Key restored in ${models.size} models | Duration: ${unfreezeDuration}ms" }
            } catch (e: Exception) {
                val unfreezeDuration = System.currentTimeMillis() - unfreezeStartTime
                log.error(e) { "Unfreeze Key | Cooldown recovery failed for ${key.id} | Failed: ${e.message} | Duration: ${unfreezeDuration}ms" }
            }
        }, coldDownTime.inWholeMilliseconds, TimeUnit.MILLISECONDS)

        val duration = System.currentTimeMillis() - startTime
        log.info { "Freeze Key | Key ${key.id} frozen for ${coldDownTime.inWholeMilliseconds}ms in ${models.size} models | Duration: ${duration}ms" }
    }

    /**
     * 获取模型的密钥统计信息
     * @param modelName 模型名称
     * @return 密钥统计信息映射
     */
    fun getModelKeyStats(modelName: String): Map<String, Any>? {
        return getModelLock(modelName).read {
            val scheduler = modelSchedulers[modelName]
            scheduler?.statsMap?.mapValues { (_, stats) ->
                mapOf(
                    "weight" to stats.weight,
                    "errorRate" to stats.errorRate,
                    "score" to stats.score,
                    "totalRequests" to stats.totalRequests,
                    "successRequests" to stats.successRequests
                )
            }
        }
    }

    /**
     * 禁用密钥（更新数据库状态并从内存中移除）
     * @param keyId 密钥ID
     */
    fun disableKey(keyId: Long) {
        val startTime = System.currentTimeMillis()
        try {
            // 首先获取密钥信息以检查 autoDisable 标志
            val keyInfo = commonTokenKeyRepository.findById(keyId).orElse(null)
            if (keyInfo == null) {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Disable Key | Key $keyId not found in database | Duration: ${duration}ms" }
                return
            }

            // 检查是否允许自动禁用
            if (!keyInfo.autoDisable) {
                val duration = System.currentTimeMillis() - startTime
                log.warn { "Disable Key | Key $keyId (${keyInfo.name ?: "unnamed"}) failed but autoDisable is disabled, skipping automatic disable | Duration: ${duration}ms" }
                return
            }

            // 更新数据库中的密钥状态为 AUTH_FAILED
            tokenManagementService.updateKeyStatus(keyId, CommonKeyStatus.AUTH_FAILED)

            // 从内存中移除密钥
            val models = keyToModelsIndex[keyId]?.toList() ?: emptyList()
            models.forEach { modelName ->
                removeKeyFromModel(modelName, keyId)
            }

            val duration = System.currentTimeMillis() - startTime
            log.warn { "Disable Key | Key $keyId (${keyInfo.name ?: "unnamed"}) has been disabled and removed from ${models.size} models due to failure | Duration: ${duration}ms" }
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Disable Key | Failed to disable key $keyId | Duration: ${duration}ms" }
        }
    }

    /**
     * 更新调度器配置
     * @param modelName 模型名称
     * @param configUpdater 配置更新函数
     */
    fun updateSchedulerConfig(modelName: String, configUpdater: SchedulerConfig.() -> Unit) {
        getModelLock(modelName).read {
            val scheduler = modelSchedulers[modelName]
            scheduler?.updateConfig(configUpdater)
            log.info { "Update Config | Scheduler config updated for model $modelName" }
        }
    }

    /**
     * 获取所有已加载的模型名称
     * @return 模型名称列表
     */
    fun getAllModelNames(): Set<String> {
        return modelKeysMap.keys.toSet()
    }

    /**
     * 获取模型的调度器配置
     * @param modelName 模型名称
     * @return 调度器配置信息
     */
    fun getSchedulerConfig(modelName: String): SchedulerConfig? {
        return getModelLock(modelName).read {
            val scheduler = modelSchedulers[modelName]
            scheduler?.getConfig()
        }
    }

}
