package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ChannelSyncStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

/**
 * Channel同步状态数据访问层
 */
@Repository
interface ChannelSyncStatusRepository : JpaRepository<ChannelSyncStatus, Long> {

    /**
     * 根据同步类型查找同步状态
     * 
     * @param syncType 同步类型标识
     * @return 同步状态记录
     */
    fun findBySyncType(syncType: String): Optional<ChannelSyncStatus>

    /**
     * 根据同步类型删除记录
     * 
     * @param syncType 同步类型标识
     */
    fun deleteBySyncType(syncType: String)
}
