package io.cliveyou.claudecodeproxybackend.sync.facade

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.sync.application.service.ChannelSyncTestService
import io.cliveyou.claudecodeproxybackend.sync.application.timer.ChannelSyncTimer
import io.cliveyou.claudecodeproxybackend.sync.domain.ChannelSyncService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.*

/**
 * Channel同步控制器
 * 提供手动触发同步的API接口
 */
@RestController
@RequestMapping("/api/v1/sync")
class ChannelSyncController(
    private val channelSyncTimer: ChannelSyncTimer,
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 手动触发Google Vertex AI密钥同步
     */
    @PostMapping("/google-vertex-ai/trigger")
    fun triggerGoogleVertexAISync(): PlatformResult<ChannelSyncService.SyncResult> {
        return try {
            log.info { "Channel Sync API | Manual sync triggered via API" }
            
            val result = channelSyncTimer.triggerManualSync()
            
            if (result.success) {
                PlatformResult.success(result)
            } else {
                PlatformResult.error("SYNC_FAILED", result.message)
            }
            
        } catch (e: Exception) {
            log.error(e) { "Channel Sync API | Manual sync failed | Failed: ${e.message}" }
            PlatformResult.error("SYNC_ERROR", "Sync failed: ${e.message}")
        }
    }

    /**
     * 手动触发Azure OpenAI密钥同步
     */
    @PostMapping("/azure-openai/trigger")
    fun triggerAzureOpenAISync(): PlatformResult<*> {
        return try {
            log.info { "Azure Channel Sync API | Manual sync triggered via API" }

            val result = channelSyncTimer.triggerManualAzureSync()

            if (result.success) {
                PlatformResult.success(result)
            } else {
                PlatformResult.error("SYNC_FAILED", result.message)
            }

        } catch (e: Exception) {
            log.error(e) { "Azure Channel Sync API | Manual sync failed via API | Failed: ${e.message}" }
            PlatformResult.error("SYNC_ERROR", "同步过程中发生错误: ${e.message}")
        }
    }

    /**
     * 获取Google Vertex AI同步状态信息
     */
    @GetMapping("/google-vertex-ai/status")
    fun getGoogleVertexAISyncStatus(): PlatformResult<Map<String, Any>> {
        return try {
            // 这里可以添加获取同步状态的逻辑
            val status = mapOf(
                "syncType" to "google_vertex_ai",
                "message" to "Use POST /api/v1/sync/google-vertex-ai/trigger to trigger manual sync"
            )

            PlatformResult.success(status)

        } catch (e: Exception) {
            log.error(e) { "Channel Sync API | Failed to get sync status | Failed: ${e.message}" }
            PlatformResult.error("STATUS_ERROR", "获取状态失败: ${e.message}")
        }
    }

    /**
     * 获取Azure OpenAI同步状态信息
     */
    @GetMapping("/azure-openai/status")
    fun getAzureOpenAISyncStatus(): PlatformResult<Map<String, Any>> {
        return try {
            // 这里可以添加获取同步状态的逻辑
            val status = mapOf(
                "syncType" to "azure_openai",
                "message" to "Use POST /api/v1/sync/azure-openai/trigger to trigger manual sync"
            )

            PlatformResult.success(status)

        } catch (e: Exception) {
            log.error(e) { "Azure Channel Sync API | Failed to get sync status | Failed: ${e.message}" }
            PlatformResult.error("STATUS_ERROR", "获取Azure同步状态失败: ${e.message}")
        }
    }
}
