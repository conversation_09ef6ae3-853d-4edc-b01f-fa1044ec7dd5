package io.cliveyou.claudecodeproxybackend.sync.domain

import com.fasterxml.jackson.databind.ObjectMapper
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.TokenManagementService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.ChannelSyncStatusRepository
import io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity.Channel
import io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository.ChannelRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Channel同步服务
 * 负责从GCP MySQL同步Google Vertex AI密钥数据到本地系统
 */
@Service
class ChannelSyncService(
    private val channelRepository: ChannelRepository,
    private val channelSyncStatusRepository: ChannelSyncStatusRepository,
    private val tokenManagementService: TokenManagementService,
    private val keyManageService: KeyManageService,
    private val objectMapper: ObjectMapper
) {
    
    private val log = KotlinLogging.logger {}
    
    companion object {
        private const val SYNC_TYPE_GOOGLE_VERTEX_AI = "google_vertex_ai"
        private const val SYNC_STATUS_SUCCESS = "SUCCESS"
        private const val SYNC_STATUS_FAILED = "FAILED"
        private const val SYNC_STATUS_RUNNING = "RUNNING"
    }

    /**
     * 执行增量同步
     * 只同步上次同步后新增的记录
     */
    @Transactional("pgTransactionManager")
    suspend fun performIncrementalSync(): SyncResult {
        val startTime = System.currentTimeMillis()
        log.info { "Channel Sync | Incremental sync started | Info: Starting Google Vertex AI channel sync" }

        try {
            // 获取上次同步状态
            val lastSyncStatus = getLastSyncStatus()
            val lastSyncId = lastSyncStatus?.lastSyncId ?: 0L

            log.info { "Channel Sync | Last sync ID retrieved | Info: Last sync ID = $lastSyncId" }

            // 查询需要同步的Channel记录
            val channelsToSync = channelRepository.findChannelsForSync(lastSyncId)

            if (channelsToSync.isEmpty()) {
                log.info { "Channel Sync | No new channels found | Info: All channels are up to date" }
                return SyncResult(
                    success = true,
                    syncCount = 0,
                    message = "No new channels to sync",
                    duration = System.currentTimeMillis() - startTime
                )
            }

            log.info { "Channel Sync | Channels found for sync | Info: Found ${channelsToSync.size} channels to sync" }

            // 更新同步状态为运行中
            updateSyncStatus(SYNC_TYPE_GOOGLE_VERTEX_AI, lastSyncId, SYNC_STATUS_RUNNING, null, 0)

            // 执行同步
            var syncCount = 0
            var maxSyncedId = lastSyncId

            for (channel in channelsToSync) {
                try {
                    if (syncChannelToLocalSystem(channel)) {
                        syncCount++
                        maxSyncedId = channel.id ?: maxSyncedId
                        log.debug { "Channel Sync | Channel synced successfully | Info: Channel ID = ${channel.id}, Name = ${channel.name}" }
                    }
                } catch (e: Exception) {
                    log.error(e) { "Channel Sync | Failed to sync channel | Failed: Channel ID = ${channel.id}, Error = ${e.message}" }
                    // 继续处理其他记录，不因单个记录失败而中断整个同步
                }
            }

            // 更新同步状态为成功
            updateSyncStatus(SYNC_TYPE_GOOGLE_VERTEX_AI, maxSyncedId, SYNC_STATUS_SUCCESS, null, syncCount)

            val duration = System.currentTimeMillis() - startTime
            log.info { "Channel Sync | Incremental sync completed | Success: Synced $syncCount channels | Duration: ${duration}ms" }

            return SyncResult(
                success = true,
                syncCount = syncCount,
                message = "Successfully synced $syncCount channels",
                duration = duration
            )

        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Channel Sync | Incremental sync failed | Failed: ${e.message} | Duration: ${duration}ms" }

            // 更新同步状态为失败
            updateSyncStatus(SYNC_TYPE_GOOGLE_VERTEX_AI, 0L, SYNC_STATUS_FAILED, e.message, 0)

            return SyncResult(
                success = false,
                syncCount = 0,
                message = "Sync failed: ${e.message}",
                duration = duration
            )
        }
    }

    /**
     * 将Channel数据同步到本地系统
     */
    private suspend fun syncChannelToLocalSystem(channel: Channel): Boolean {
        try {
            // 验证是否为Google Vertex AI类型的密钥
            if (!isGoogleVertexAIKey(channel.key)) {
                log.debug { "Channel Sync | Skipping non-Google Vertex AI channel | Info: Channel ID = ${channel.id}" }
                return false
            }

            // 解析Google Vertex AI JSON配置
            val metadata = extractGoogleVertexAIMetadata(channel.key)

            // 解析服务账户JSON以获取详细信息
            val serviceAccount = parseServiceAccountJson(channel.key)

            // 创建CommonTokenKey记录
            val commonTokenKey = CommonTokenKey(
                accessToken = null, // Google Vertex AI 初始没有 access token
                domain = null,
                type = KeyChannel.GoogleVertexAI,
                status = if (channel.status == 1L) CommonKeyStatus.ACTIVE else CommonKeyStatus.DISABLED,
                name = channel.name ?: "Google Vertex AI Key ${channel.id}",
                supportModels = listOf("gemini-2.5-pro","gemini-2.5-flash"),
                autoDisable = true,
                quota = channel.balance?.toBigDecimal(),
                weight = BigDecimal.ONE,
                windowSize = 100,
                epsilon = BigDecimal("0.05"),
                errThreshold = BigDecimal("0.5"),
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            // 创建Google Vertex AI刷新信息
            val refreshInfo = GoogleVertexAITokenRefreshInfo(
                commonKeyId = 0, // 将在 tokenManagementService 中设置
                projectId = serviceAccount.project_id,
                clientEmail = serviceAccount.client_email,
                privateKeyId = serviceAccount.private_key_id,
                privateKey = serviceAccount.private_key,
                tokenUri = serviceAccount.token_uri,
                scope = "https://www.googleapis.com/auth/cloud-platform",
                expiresAt = null,
                lastRefreshTime = null,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            // 保存到数据库
            val savedKey = tokenManagementService.createTokenKey(commonTokenKey, googleRefreshInfo = refreshInfo)

            // Google Vertex AI密钥初始没有access token，需要通过刷新获取
            // 这里不直接加载到内存，而是等待后续的token刷新流程
            log.info { "Channel Sync | Google Vertex AI key saved | Info: Key ID = ${savedKey.id}, will be loaded to memory after token refresh" }

            return true

        } catch (e: Exception) {
            log.error(e) { "Channel Sync | Failed to sync channel to local system | Failed: Channel ID = ${channel.id}, Error = ${e.message}" }
            throw e
        }
    }

    /**
     * 验证是否为Google Vertex AI密钥
     */
    private fun isGoogleVertexAIKey(keyContent: String): Boolean {
        return try {
            val jsonNode = objectMapper.readTree(keyContent)
            jsonNode.has("type") &&
            jsonNode.get("type").asText() == "service_account" &&
            jsonNode.has("project_id") &&
            jsonNode.has("private_key")
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 提取Google Vertex AI元数据
     */
    private fun extractGoogleVertexAIMetadata(keyContent: String): String {
        val jsonNode = objectMapper.readTree(keyContent)
        val metadata = mapOf(
            "projectId" to jsonNode.get("project_id")?.asText(),
            "clientEmail" to jsonNode.get("client_email")?.asText(),
            "clientId" to jsonNode.get("client_id")?.asText()
        ).filterValues { it != null }

        return objectMapper.writeValueAsString(metadata)
    }

    /**
     * 解析服务账户JSON
     */
    private fun parseServiceAccountJson(serviceAccountJson: String): ServiceAccount {
        return try {
            objectMapper.readValue(serviceAccountJson, ServiceAccount::class.java)
        } catch (e: Exception) {
            log.error(e) { "Channel Sync | Failed to parse service account JSON | Failed: ${e.message}" }
            throw IllegalArgumentException("Invalid service account JSON format", e)
        }
    }

    /**
     * 提取支持的模型列表
     */
    private fun extractSupportedModels(modelsJson: String?): List<String> {
        if (modelsJson.isNullOrBlank()) {
            return listOf("gemini-pro", "gemini-pro-vision") // 默认支持的模型
        }

        return try {
            objectMapper.readValue(modelsJson, List::class.java) as List<String>
        } catch (e: Exception) {
            log.warn { "Channel Sync | Failed to parse models JSON | Warning: Using default models | Error = ${e.message}" }
            listOf("gemini-pro", "gemini-pro-vision")
        }
    }

    /**
     * 获取上次同步状态
     */
    private fun getLastSyncStatus(): ChannelSyncStatus? {
        return channelSyncStatusRepository.findBySyncType(SYNC_TYPE_GOOGLE_VERTEX_AI).orElse(null)
    }

    /**
     * 更新同步状态
     */
    private fun updateSyncStatus(
        syncType: String,
        lastSyncId: Long,
        status: String,
        errorMessage: String?,
        syncCount: Int
    ) {
        val existingStatus = channelSyncStatusRepository.findBySyncType(syncType).orElse(null)

        val syncStatus = if (existingStatus != null) {
            existingStatus.copy(
                lastSyncId = lastSyncId,
                lastSyncTime = LocalDateTime.now(),
                syncStatus = status,
                errorMessage = errorMessage,
                syncCount = syncCount
            )
        } else {
            ChannelSyncStatus(
                syncType = syncType,
                lastSyncId = lastSyncId,
                lastSyncTime = LocalDateTime.now(),
                syncStatus = status,
                errorMessage = errorMessage,
                syncCount = syncCount
            )
        }

        channelSyncStatusRepository.save(syncStatus)
    }

    /**
     * 服务账户JSON数据类
     */
    data class ServiceAccount(
        val type: String,
        val project_id: String,
        val private_key_id: String,
        val private_key: String,
        val client_email: String,
        val client_id: String,
        val auth_uri: String,
        val token_uri: String,
        val auth_provider_x509_cert_url: String,
        val client_x509_cert_url: String
    )

    /**
     * 同步结果数据类
     */
    data class SyncResult(
        val success: Boolean,
        val syncCount: Int,
        val message: String,
        val duration: Long
    )
}
