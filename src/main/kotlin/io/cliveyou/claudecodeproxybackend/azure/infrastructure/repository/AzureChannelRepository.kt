package io.cliveyou.claudecodeproxybackend.azure.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity.AzureChannel
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * Azure Channel数据访问层
 * 用于查询Azure MySQL数据库中的channels表
 */
@Repository
interface AzureChannelRepository : JpaRepository<AzureChannel, Long> {

    /**
     * 查询状态为1且ID大于指定值的Channel记录
     * 用于增量同步
     * 
     * @param lastSyncId 上次同步的最大ID
     * @param status 状态值，默认为1（启用）
     * @return 需要同步的Channel列表
     */
    @Query("""
        SELECT c FROM AzureChannel c 
        WHERE c.status = :status 
        AND c.id > :lastSyncId 
        ORDER BY c.id ASC
    """)
    fun findChannelsForSync(
        @Param("lastSyncId") lastSyncId: Long,
        @Param("status") status: Long = 1L
    ): List<AzureChannel>

    /**
     * 查询状态为1的所有Channel记录
     * 用于全量同步
     * 
     * @param status 状态值，默认为1（启用）
     * @return 所有启用的Channel列表
     */
    @Query("""
        SELECT c FROM AzureChannel c 
        WHERE c.status = :status 
        ORDER BY c.id ASC
    """)
    fun findAllActiveChannels(
        @Param("status") status: Long = 1L
    ): List<AzureChannel>

    /**
     * 获取最大的Channel ID
     * 用于初始化同步状态
     * 
     * @return 最大ID，如果没有记录则返回null
     */
    @Query("SELECT MAX(c.id) FROM AzureChannel c")
    fun findMaxChannelId(): Long?

    /**
     * 根据base_url和key查找是否已存在相同的Channel
     * 用于避免重复同步
     * 
     * @param baseUrl 基础URL
     * @param key API密钥
     * @return 匹配的Channel记录
     */
    @Query("""
        SELECT c FROM AzureChannel c 
        WHERE c.baseUrl = :baseUrl 
        AND c.key = :key
    """)
    fun findByBaseUrlAndKey(
        @Param("baseUrl") baseUrl: String?,
        @Param("key") key: String
    ): List<AzureChannel>
}
