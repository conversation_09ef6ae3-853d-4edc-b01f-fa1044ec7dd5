package io.cliveyou.claudecodeproxybackend.azure.domain

import com.fasterxml.jackson.databind.ObjectMapper
import io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity.AzureChannel
import io.cliveyou.claudecodeproxybackend.azure.infrastructure.repository.AzureChannelRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.TokenManagementService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.ChannelSyncStatusRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Azure Channel同步服务
 * 负责从Azure MySQL同步OpenAI密钥数据到本地系统
 */
@Service
class AzureChannelSyncService(
    private val azureChannelRepository: AzureChannelRepository,
    private val channelSyncStatusRepository: ChannelSyncStatusRepository,
    private val tokenManagementService: TokenManagementService,
    private val keyManageService: KeyManageService,
) {
    
    private val log = KotlinLogging.logger {}
    
    companion object {
        private const val SYNC_TYPE_AZURE_OPENAI = "azure_openai"
        private const val SYNC_STATUS_SUCCESS = "SUCCESS"
        private const val SYNC_STATUS_FAILED = "FAILED"
        private const val SYNC_STATUS_RUNNING = "RUNNING"
        
        // 支持的AI模型列表
        private val SUPPORTED_MODELS = listOf(
            "gpt-3.5-turbo-0125", "chatgpt-4o-latest", "o1-2024-12-17", "gpt-image-1", 
            "o4-mini-2025-04-16", "o3-2025-04-16", "gpt-4-turbo-2024-04-09", 
            "gpt-4o-mini-2024-07-18", "o3-mini-2025-01-31", "o1-preview-2024-09-12", 
            "gpt-4o-2024-11-20", "o1-mini-2024-09-12", "gpt-4.1-nano-2025-04-14", 
            "gpt-4.1-mini-2025-04-14", "gpt-4.1-2025-04-14", "gpt-4-0125-preview", 
            "gpt-4-0613", "gpt-4o-2024-08-06", "gpt-4-1106-preview", "gpt-4o-2024-05-13", 
            "gpt-4.5-preview-2025-02-27"
        )
    }

    /**
     * 执行增量同步
     * 只同步上次同步后新增的记录
     */
    @Transactional("pgTransactionManager")
    suspend fun performIncrementalSync(): SyncResult {
        val startTime = System.currentTimeMillis()
        log.info { "Azure Channel Sync | Incremental sync started | Info: Starting Azure OpenAI channel sync" }

        try {
            // 获取上次同步状态
            val lastSyncStatus = getLastSyncStatus()
            val lastSyncId = lastSyncStatus?.lastSyncId ?: 0L

            log.info { "Azure Channel Sync | Last sync ID retrieved | Info: Last sync ID = $lastSyncId" }

            // 查询需要同步的Channel记录
            val channelsToSync = azureChannelRepository.findChannelsForSync(lastSyncId)

            if (channelsToSync.isEmpty()) {
                log.info { "Azure Channel Sync | No new channels found | Info: All channels are up to date" }
                return SyncResult(
                    success = true,
                    syncCount = 0,
                    message = "No new channels to sync",
                    duration = System.currentTimeMillis() - startTime
                )
            }

            log.info { "Azure Channel Sync | Channels found for sync | Info: Found ${channelsToSync.size} channels to sync" }

            // 更新同步状态为运行中
            updateSyncStatus(SYNC_TYPE_AZURE_OPENAI, lastSyncId, SYNC_STATUS_RUNNING, null, 0)

            // 执行同步
            var syncCount = 0
            var maxSyncedId = lastSyncId

            for (channel in channelsToSync) {
                try {
                    if (syncChannelToLocalSystem(channel)) {
                        syncCount++
                        maxSyncedId = channel.id ?: maxSyncedId
                        log.debug { "Azure Channel Sync | Channel synced successfully | Info: Channel ID = ${channel.id}, Name = ${channel.name}" }
                    }
                } catch (e: Exception) {
                    log.error(e) { "Azure Channel Sync | Failed to sync channel | Failed: Channel ID = ${channel.id}, Error = ${e.message}" }
                    // 继续处理其他记录，不因单个记录失败而中断整个同步
                }
            }

            // 更新同步状态为成功
            updateSyncStatus(SYNC_TYPE_AZURE_OPENAI, maxSyncedId, SYNC_STATUS_SUCCESS, null, syncCount)

            val duration = System.currentTimeMillis() - startTime
            log.info { "Azure Channel Sync | Incremental sync completed | Success: Synced $syncCount channels | Duration: ${duration}ms" }

            return SyncResult(
                success = true,
                syncCount = syncCount,
                message = "Successfully synced $syncCount channels",
                duration = duration
            )

        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Azure Channel Sync | Incremental sync failed | Failed: ${e.message} | Duration: ${duration}ms" }

            // 更新同步状态为失败
            updateSyncStatus(SYNC_TYPE_AZURE_OPENAI, 0L, SYNC_STATUS_FAILED, e.message, 0)

            return SyncResult(
                success = false,
                syncCount = 0,
                message = "Sync failed: ${e.message}",
                duration = duration
            )
        }
    }

    /**
     * 将Azure Channel数据同步到本地系统
     */
    private suspend fun syncChannelToLocalSystem(channel: AzureChannel): Boolean {
        try {
            // 验证密钥是否有效
            if (channel.key.isBlank()) {
                log.debug { "Azure Channel Sync | Skipping channel with empty key | Info: Channel ID = ${channel.id}" }
                return false
            }

            // 创建CommonTokenKey记录
            val commonTokenKey = CommonTokenKey(
                accessToken = channel.key,
                domain = channel.baseUrl,
                type = KeyChannel.Azure, // Azure channels 使用 OpenAI 兼容接口
                status = if (channel.status == 1L) CommonKeyStatus.ACTIVE else CommonKeyStatus.DISABLED,
                name = channel.name ?: "Azure OpenAI Key ${channel.id}",
                supportModels = SUPPORTED_MODELS,
                autoDisable = true,
                quota = channel.balance?.toBigDecimal(),
                weight = BigDecimal.ONE,
                windowSize = 100,
                epsilon = BigDecimal("0.05"),
                errThreshold = BigDecimal("0.5"),
                minSampleSize = 20,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            // 保存到数据库 (Azure OpenAI密钥不需要额外的刷新信息)
            val savedKey = tokenManagementService.createTokenKey(commonTokenKey)

            // 如果密钥是活跃状态且有访问令牌，则将密钥添加到内存中的密钥管理
            if (savedKey.status == CommonKeyStatus.ACTIVE && !savedKey.accessToken.isNullOrBlank()) {
                // 将密钥添加到其支持的每个模型中，使用数据库中的权重
                val weight = savedKey.weight.toDouble()
                savedKey.supportModels.forEach { modelName ->
                    keyManageService.addKeyToModel(modelName, savedKey, weight)
                }
                log.info { "Azure Channel Sync | Key added to memory | Info: Key ID = ${savedKey.id}, Models = ${savedKey.supportModels.size}, Weight = $weight" }
            }

            return true

        } catch (e: Exception) {
            log.error(e) { "Azure Channel Sync | Failed to sync channel to local system | Failed: Channel ID = ${channel.id}, Error = ${e.message}" }
            throw e
        }
    }


    /**
     * 获取上次同步状态
     */
    private fun getLastSyncStatus(): ChannelSyncStatus? {
        return channelSyncStatusRepository.findBySyncType(SYNC_TYPE_AZURE_OPENAI).orElse(null)
    }

    /**
     * 更新同步状态
     */
    private fun updateSyncStatus(
        syncType: String,
        lastSyncId: Long,
        status: String,
        errorMessage: String?,
        syncCount: Int
    ) {
        val existingStatus = channelSyncStatusRepository.findBySyncType(syncType).orElse(null)

        val syncStatus = if (existingStatus != null) {
            existingStatus.copy(
                lastSyncId = lastSyncId,
                lastSyncTime = LocalDateTime.now(),
                syncStatus = status,
                errorMessage = errorMessage,
                syncCount = syncCount
            )
        } else {
            ChannelSyncStatus(
                syncType = syncType,
                lastSyncId = lastSyncId,
                lastSyncTime = LocalDateTime.now(),
                syncStatus = status,
                errorMessage = errorMessage,
                syncCount = syncCount
            )
        }

        channelSyncStatusRepository.save(syncStatus)
    }

    /**
     * 同步结果数据类
     */
    data class SyncResult(
        val success: Boolean,
        val syncCount: Int,
        val message: String,
        val duration: Long
    )
}
