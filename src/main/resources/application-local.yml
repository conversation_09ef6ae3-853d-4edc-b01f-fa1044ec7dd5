spring:
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:root}
    driver-class-name: org.postgresql.Driver

    # MySQL数据源配置 - 用于同步GCP channels表
    mysql:
      url: ***********************************************************************************************
      username: ${MYSQL_USERNAME:gcp}
      password: ${MYSQL_PASSWORD:5BJnDPRLZ3zSDekw}
      driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    defer-datasource-initialization: true

  sql:
    init:
      mode: always
      platform: postgresql

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      enabled: true
      resolve-lazily: false

server:
  port: 8070
  tomcat:
    max-part-count: 100

logging:
  level:
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
