-- 添加Azure OpenAI同步状态记录
-- 扩展channel_sync_status表以支持Azure数据源同步

-- 插入Azure OpenAI同步的初始记录
INSERT INTO channel_sync_status (sync_type, last_sync_id, sync_status) 
VALUES ('azure_openai', 0, 'SUCCESS')
ON CONFLICT (sync_type) DO NOTHING;

-- 更新表注释
COMMENT ON TABLE channel_sync_status IS 'Channel同步状态表，记录Google Vertex AI和Azure OpenAI密钥同步的状态和进度';
COMMENT ON COLUMN channel_sync_status.sync_type IS '同步类型标识，如google_vertex_ai, azure_openai';
