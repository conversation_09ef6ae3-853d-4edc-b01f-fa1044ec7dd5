# Azure Channel 同步功能

## 概述

Azure Channel 同步功能允许系统从Azure MySQL数据库中的`channels`表同步OpenAI密钥数据到本地密钥管理系统。该功能基于现有的密钥管理架构，提供了完整的数据同步、转换和管理能力。

## 功能特性

- **增量同步**: 只同步上次同步后新增的记录，提高同步效率
- **数据转换**: 将Azure channels表数据转换为CommonTokenKey实体
- **内存管理**: 自动将同步的密钥加载到内存中的密钥管理系统
- **错误处理**: 完善的错误处理和重试机制
- **状态管理**: 记录同步状态和进度
- **定时任务**: 自动定期同步，默认每30分钟执行一次
- **手动触发**: 支持通过API手动触发同步

## 数据库配置

### Azure数据库连接信息

```yaml
spring:
  datasource:
    azure:
      url: ******************************************************************************************************
      username: root
      password: yeqiu669.
      driver-class-name: com.mysql.cj.jdbc.Driver
```

### 环境变量配置

可以通过环境变量覆盖默认配置：

```bash
AZURE_USERNAME=root
AZURE_PASSWORD=yeqiu669.
```

## 数据映射

### Azure channels表字段映射

| Azure字段 | CommonTokenKey字段 | 说明 |
|-----------|-------------------|------|
| `key` | `accessToken` | API密钥 |
| `base_url` | `domain` | 基础URL |
| `name` | `name` | 密钥名称 |
| `status` | `status` | 状态(1=ACTIVE, 0=DISABLED) |
| `balance` | `quota` | 配额余额 |
| `model_mapping` | `modelMapping` | 模型映射配置 |
| `auto_ban` | `autoDisable` | 自动禁用设置 |

### 支持的AI模型

系统会为每个同步的密钥自动配置以下支持的AI模型：

```
gpt-3.5-turbo-0125, chatgpt-4o-latest, o1-2024-12-17, gpt-image-1, 
o4-mini-2025-04-16, o3-2025-04-16, gpt-4-turbo-2024-04-09, 
gpt-4o-mini-2024-07-18, o3-mini-2025-01-31, o1-preview-2024-09-12, 
gpt-4o-2024-11-20, o1-mini-2024-09-12, gpt-4.1-nano-2025-04-14, 
gpt-4.1-mini-2025-04-14, gpt-4.1-2025-04-14, gpt-4-0125-preview, 
gpt-4-0613, gpt-4o-2024-08-06, gpt-4-1106-preview, gpt-4o-2024-05-13, 
gpt-4.5-preview-2025-02-27
```

## API接口

### 手动触发同步

```http
POST /api/v1/sync/azure-openai/trigger
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "syncCount": 5,
    "message": "Successfully synced 5 channels",
    "duration": 1250
  }
}
```

### 查询同步状态

```http
GET /api/v1/sync/azure-openai/status
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "syncType": "azure_openai",
    "message": "Use POST /api/v1/sync/azure-openai/trigger to trigger manual sync"
  }
}
```

## 定时任务

系统会自动执行定时同步任务：

- **频率**: 每30分钟执行一次
- **类型**: 增量同步
- **日志**: 详细的同步日志记录

## 同步状态管理

系统使用`channel_sync_status`表记录同步状态：

- **sync_type**: `azure_openai`
- **last_sync_id**: 上次同步的最大ID
- **last_sync_time**: 上次同步时间
- **sync_status**: 同步状态(SUCCESS/FAILED/RUNNING)
- **error_message**: 错误信息
- **sync_count**: 同步记录数

## 错误处理

### 常见错误及解决方案

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认防火墙设置

2. **密钥格式错误**
   - 系统会跳过空密钥
   - 记录详细错误日志

3. **重复密钥处理**
   - 系统会检测并跳过重复的密钥
   - 基于base_url和key字段判断重复

## 监控和日志

### 日志级别

- **INFO**: 同步开始、完成、统计信息
- **DEBUG**: 详细的同步过程信息
- **ERROR**: 同步失败和错误信息
- **WARN**: 警告信息，如数据格式问题

### 关键日志示例

```
Azure Channel Sync | Incremental sync started | Info: Starting Azure OpenAI channel sync
Azure Channel Sync | Channels found for sync | Info: Found 3 channels to sync
Azure Channel Sync | Channel synced successfully | Info: Channel ID = 123, Name = Test Channel
Azure Channel Sync | Incremental sync completed | Success: Synced 3 channels | Duration: 1250ms
```

## 内存管理

同步功能会自动将密钥加载到内存中的密钥管理系统：

1. **自动加载**: 同步成功后自动将密钥添加到内存
2. **模型映射**: 根据支持的模型列表将密钥分配到对应模型
3. **权重配置**: 使用数据库中配置的权重值
4. **状态检查**: 只有活跃状态且有有效访问令牌的密钥才会加载到内存

### 内存加载流程

```
Azure Channel 同步 → 保存到数据库 → 检查密钥状态 → 加载到内存 → 分配到模型
```

### 日志示例

```
Azure Channel Sync | Key added to memory | Info: Key ID = 123, Models = 22, Weight = 1.0
```

## 性能优化

1. **增量同步**: 只处理新增记录
2. **批量处理**: 支持批量数据处理
3. **连接池**: 使用数据库连接池
4. **异步处理**: 使用协程进行异步处理
5. **内存缓存**: 密钥加载到内存中，提高访问速度

## 安全考虑

1. **密钥保护**: 密钥在传输和存储过程中的安全性
2. **访问控制**: 限制对同步API的访问
3. **日志脱敏**: 避免在日志中暴露敏感信息
4. **网络安全**: 使用安全的数据库连接

## 故障排除

### 检查同步状态

```sql
SELECT * FROM channel_sync_status WHERE sync_type = 'azure_openai';
```

### 查看最近同步的密钥

```sql
SELECT * FROM sp_platform_common_code_key 
WHERE type = 'Openai' 
ORDER BY created_at DESC 
LIMIT 10;
```

### 重置同步状态

如需重新开始同步，可以重置同步状态：

```sql
UPDATE channel_sync_status 
SET last_sync_id = 0, sync_status = 'SUCCESS' 
WHERE sync_type = 'azure_openai';
```

## 扩展性

该同步功能设计具有良好的扩展性：

1. **多数据源支持**: 可以轻松添加新的数据源
2. **自定义转换逻辑**: 支持自定义数据转换规则
3. **插件化架构**: 基于Spring的依赖注入架构
4. **配置化管理**: 通过配置文件管理同步参数
