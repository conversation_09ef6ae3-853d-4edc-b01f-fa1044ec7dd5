# Azure Channel 同步使用示例

## 快速开始

### 1. 配置数据库连接

在 `application.yml` 中配置Azure数据库连接：

```yaml
spring:
  datasource:
    azure:
      url: ******************************************************************************************************
      username: root
      password: yeqiu669.
      driver-class-name: com.mysql.cj.jdbc.Driver
```

### 2. 启动应用

启动应用后，系统会自动：
- 创建Azure数据源连接
- 初始化同步状态
- 开始定时同步任务

### 3. 手动触发同步

使用API手动触发同步：

```bash
curl -X POST http://localhost:8070/api/v1/sync/azure-openai/trigger
```

**成功响应示例：**
```json
{
  "success": true,
  "data": {
    "success": true,
    "syncCount": 3,
    "message": "Successfully synced 3 channels",
    "duration": 1250
  }
}
```

### 4. 查询同步状态

```bash
curl -X GET http://localhost:8070/api/v1/sync/azure-openai/status
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "syncType": "azure_openai",
    "message": "Use POST /api/v1/sync/azure-openai/trigger to trigger manual sync"
  }
}
```

## 同步流程示例

### Azure channels表数据示例

```sql
SELECT id, `key`, base_url, name, status, balance, model_mapping 
FROM channels 
WHERE status = 1 
ORDER BY id DESC 
LIMIT 5;
```

**结果示例：**
```
+----+------------------+---------------------------+----------------+--------+---------+----------------+
| id | key              | base_url                  | name           | status | balance | model_mapping  |
+----+------------------+---------------------------+----------------+--------+---------+----------------+
| 15 | sk-abc123...     | https://api.openai.com/v1| Azure Key 1    |      1 |   100.0 | {"gpt-4":"..."} |
| 16 | sk-def456...     | https://api.openai.com/v1| Azure Key 2    |      1 |   200.0 | NULL           |
| 17 | sk-ghi789...     | https://custom.api.com/v1 | Custom API     |      1 |    50.0 | NULL           |
+----+------------------+---------------------------+----------------+--------+---------+----------------+
```

### 同步后的CommonTokenKey数据

```sql
SELECT id, access_token, domain, type, status, name, support_models, weight 
FROM sp_platform_common_code_key 
WHERE type = 'Openai' 
ORDER BY created_at DESC 
LIMIT 3;
```

**结果示例：**
```
+----+------------------+---------------------------+--------+--------+----------------+------------------+--------+
| id | access_token     | domain                    | type   | status | name           | support_models   | weight |
+----+------------------+---------------------------+--------+--------+----------------+------------------+--------+
| 45 | sk-abc123...     | https://api.openai.com/v1| Openai | ACTIVE | Azure Key 1    | {gpt-3.5-turbo...| 1.00   |
| 46 | sk-def456...     | https://api.openai.com/v1| Openai | ACTIVE | Azure Key 2    | {gpt-3.5-turbo...| 1.00   |
| 47 | sk-ghi789...     | https://custom.api.com/v1 | Openai | ACTIVE | Custom API     | {gpt-3.5-turbo...| 1.00   |
+----+------------------+---------------------------+--------+--------+----------------+------------------+--------+
```

## 日志示例

### 成功同步日志

```
2025-01-29 10:30:00.123 INFO  --- Azure Channel Sync | Incremental sync started | Info: Starting Azure OpenAI channel sync
2025-01-29 10:30:00.145 INFO  --- Azure Channel Sync | Last sync ID retrieved | Info: Last sync ID = 10
2025-01-29 10:30:00.234 INFO  --- Azure Channel Sync | Channels found for sync | Info: Found 3 channels to sync
2025-01-29 10:30:00.456 INFO  --- Create Token | Created CommonTokenKey with id: 45, type: Openai
2025-01-29 10:30:00.467 INFO  --- Azure Channel Sync | Key added to memory | Info: Key ID = 45, Models = 22, Weight = 1.0
2025-01-29 10:30:00.567 INFO  --- Create Token | Created CommonTokenKey with id: 46, type: Openai
2025-01-29 10:30:00.578 INFO  --- Azure Channel Sync | Key added to memory | Info: Key ID = 46, Models = 22, Weight = 1.0
2025-01-29 10:30:00.678 INFO  --- Create Token | Created CommonTokenKey with id: 47, type: Openai
2025-01-29 10:30:00.689 INFO  --- Azure Channel Sync | Key added to memory | Info: Key ID = 47, Models = 22, Weight = 1.0
2025-01-29 10:30:00.789 INFO  --- Azure Channel Sync | Incremental sync completed | Success: Synced 3 channels | Duration: 666ms
```

### 内存加载日志

```
2025-01-29 10:30:00.467 INFO  --- Add Key | Key 45 added to model gpt-3.5-turbo-0125 with weight 1.0 | Duration: 2ms
2025-01-29 10:30:00.469 INFO  --- Add Key | Key 45 added to model chatgpt-4o-latest with weight 1.0 | Duration: 1ms
2025-01-29 10:30:00.471 INFO  --- Add Key | Key 45 added to model gpt-4o-mini-2024-07-18 with weight 1.0 | Duration: 1ms
...
```

## 错误处理示例

### 数据库连接失败

```
2025-01-29 10:30:00.123 ERROR --- Azure Channel Sync | Incremental sync failed | Failed: Connection refused | Duration: 50ms
```

### 密钥格式错误

```
2025-01-29 10:30:00.456 DEBUG --- Azure Channel Sync | Skipping channel with empty key | Info: Channel ID = 18
```

### 重复密钥检测

```
2025-01-29 10:30:00.567 DEBUG --- Azure Channel Sync | Skipping duplicate channel | Info: Channel ID = 19
```

## 监控和维护

### 检查同步状态

```sql
-- 查看最近的同步状态
SELECT * FROM channel_sync_status WHERE sync_type = 'azure_openai';

-- 查看最近同步的密钥数量
SELECT COUNT(*) as synced_keys 
FROM sp_platform_common_code_key 
WHERE type = 'Openai' 
AND created_at > NOW() - INTERVAL 1 HOUR;
```

### 重置同步状态

如果需要重新同步所有数据：

```sql
-- 重置同步状态
UPDATE channel_sync_status 
SET last_sync_id = 0, sync_status = 'SUCCESS' 
WHERE sync_type = 'azure_openai';
```

### 查看内存中的密钥

通过日志可以看到密钥是否成功加载到内存：

```bash
# 查看最近的密钥加载日志
grep "Key.*added to model" application.log | tail -20
```

## 性能监控

### 同步性能指标

- **同步频率**: 每30分钟自动执行
- **平均同步时间**: 通常在1-5秒内完成
- **内存加载时间**: 每个密钥约1-2毫秒

### 优化建议

1. **网络优化**: 确保应用服务器与Azure数据库之间的网络连接稳定
2. **索引优化**: 在Azure数据库的channels表上创建适当的索引
3. **批量处理**: 对于大量数据，考虑调整批量处理大小
4. **监控告警**: 设置同步失败的告警机制

## 故障排除

### 常见问题

1. **同步失败**: 检查数据库连接和网络
2. **密钥未生效**: 确认密钥状态为ACTIVE且有有效的access_token
3. **内存未加载**: 检查密钥的supportModels配置

### 调试步骤

1. 检查应用日志中的错误信息
2. 验证数据库连接配置
3. 手动触发同步并观察日志
4. 检查数据库中的同步状态记录
