# Google Vertex AI 同步功能设置指南

## 当前状态

✅ **双数据源配置已完成**！MySQL和PostgreSQL同步功能已经**完全启用**。

## 已完成的工作

### 1. 数据库配置
- ✅ 添加了MySQL驱动依赖
- ✅ 配置了MySQL数据源（暂时禁用）
- ✅ 创建了Channel实体映射
- ✅ 创建了ChannelSyncStatus实体（使用PostgreSQL）

### 2. 同步框架
- ✅ 创建了ChannelSyncService（完整功能已启用）
- ✅ 创建了ChannelSyncTimer（定时任务已启用，每30分钟执行）
- ✅ 创建了测试API接口

### 3. API接口
- ✅ `GET /api/v1/sync/test-connections` - 测试双数据源连接
- ✅ `POST /api/v1/sync/google-vertex-ai/trigger` - 手动触发同步
- ✅ `GET /api/v1/sync/google-vertex-ai/status` - 获取同步状态

### 4. 双数据源配置
- ✅ **主数据源（PostgreSQL）**: 使用标准bean名称（dataSource, entityManagerFactory, transactionManager）
- ✅ **辅助数据源（MySQL）**: 使用自定义bean名称（mysqlDataSource, mysqlEntityManagerFactory, mysqlTransactionManager）
- ✅ **包路径分离**: keymanagment包使用PostgreSQL，sync包使用MySQL

## 双数据源配置详情

### 配置文件结构
```yaml
spring:
  datasource:
    pg:
      url: *****************************************
      username: postgres
      password: password
      driver-class-name: org.postgresql.Driver
    mysql:
      url: *******************************
      username: mysql
      password: password
      driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    pg:
      properties:
        hibernate:
          ddl-auto: update
          dialect: org.hibernate.dialect.PostgreSQLDialect
    mysql:
      properties:
        hibernate:
          ddl-auto: none
          dialect: org.hibernate.dialect.MySQLDialect
```

### 主数据源（PostgreSQL）
```kotlin
@Primary
@Bean(name = ["pgDataSource"])
@ConfigurationProperties(prefix = "spring.datasource.pg")
fun pgDataSource(): DataSource

@Primary
@Bean(name = ["pgEntityManagerFactory"])
fun pgEntityManagerFactory(
    @Qualifier("pgDataSource") dataSource: DataSource,
    builder: EntityManagerFactoryBuilder
): LocalContainerEntityManagerFactoryBean
```

### 辅助数据源（MySQL）
```kotlin
@Bean(name = ["mysqlDataSource"])
@ConfigurationProperties(prefix = "spring.datasource.mysql")
fun mysqlDataSource(): DataSource

@Bean(name = ["mysqlEntityManagerFactory"])
fun mysqlEntityManagerFactory(
    @Qualifier("mysqlDataSource") dataSource: DataSource,
    builder: EntityManagerFactoryBuilder
): LocalContainerEntityManagerFactoryBean
```

### Repository包路径分离
- **PostgreSQL**: `io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository`
- **MySQL**: `io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository`

### 事务管理器使用
```kotlin
// PostgreSQL事务
@Transactional("pgTransactionManager")
fun saveUserToPg(user: User): User

// MySQL事务
@Transactional("mysqlTransactionManager")
fun saveChannelToMysql(channel: Channel): Channel
```

## 测试步骤

### 1. 启动应用
```bash
./gradlew bootRun
```

### 2. 测试数据源连接
```bash
# 测试Repository层连接
curl -X GET http://localhost:8070/api/v1/sync/test-connections

# 测试DataSource层连接
curl -X GET http://localhost:8070/api/v1/sync/test-datasources
```

应该返回：
```json
{
  "code": "200",
  "desc": "操作成功",
  "data": {
    "mysql_connection": "SUCCESS",
    "mysql_channel_count": 123,
    "postgresql_connection": "SUCCESS",
    "postgresql_sync_status_count": 1
  },
  "success": true
}
```

### 3. 测试同步API
```bash
curl -X POST http://localhost:8070/api/v1/sync/google-vertex-ai/trigger
```

应该返回：
```json
{
  "code": "200",
  "desc": "操作成功",
  "data": {
    "success": true,
    "syncCount": 5,
    "message": "Successfully synced 5 channels",
    "duration": 1250
  },
  "success": true
}
```

## 故障排除

如果应用仍然无法启动，请检查：

1. **PostgreSQL连接**：确保PostgreSQL数据库可访问
2. **依赖冲突**：检查是否有其他JPA配置冲突
3. **端口占用**：确保8070端口未被占用

## 功能特性

### 🔄 **自动同步**
- 每30分钟自动执行增量同步
- 基于ID的增量同步，避免重复处理
- 完整的错误处理和重试机制

### 🔍 **数据验证**
- 验证Google Vertex AI服务账户JSON格式
- 提取项目ID、客户端邮箱等元数据
- 支持模型列表解析和映射

### 📊 **状态追踪**
- 记录上次同步的最大ID
- 追踪同步状态（SUCCESS, FAILED, RUNNING）
- 详细的错误信息记录

### 🛡️ **安全性**
- 独立的事务管理器确保数据一致性
- 单个记录失败不影响整体同步
- 完整的日志记录便于排查问题
